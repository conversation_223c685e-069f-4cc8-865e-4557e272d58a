import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QPushButton, QLabel, QFrame, QGraphicsDropShadowEffect)
from PySide6.QtCore import Qt, QPoint, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QPainter, QColor, QFont, QIcon, QPalette, QPixmap

class TitleBar(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setFixedHeight(40)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(32, 32, 32, 200);
                border: none;
            }
        """)
        
        # 创建布局
        layout = QHBoxLayout()
        layout.setContentsMargins(10, 0, 10, 0)
        layout.setSpacing(0)
        
        # 应用标题
        self.title_label = QLabel("Fluent UI App")
        self.title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
        """)
        
        # 窗口控制按钮
        self.minimize_btn = self.create_title_button("−", self.minimize_window)
        self.maximize_btn = self.create_title_button("□", self.maximize_window)
        self.close_btn = self.create_title_button("×", self.close_window)
        self.close_btn.setStyleSheet(self.close_btn.styleSheet() + """
            QPushButton:hover {
                background-color: #e81123;
            }
        """)
        
        layout.addWidget(self.title_label)
        layout.addStretch()
        layout.addWidget(self.minimize_btn)
        layout.addWidget(self.maximize_btn)
        layout.addWidget(self.close_btn)
        
        self.setLayout(layout)
        
        # 用于拖拽窗口
        self.start_pos = None
        
    def create_title_button(self, text, callback):
        btn = QPushButton(text)
        btn.setFixedSize(45, 30)
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                border: none;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 30);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 50);
            }
        """)
        btn.clicked.connect(callback)
        return btn
    
    def minimize_window(self):
        self.parent.showMinimized()
    
    def maximize_window(self):
        if self.parent.isMaximized():
            self.parent.showNormal()
            self.maximize_btn.setText("□")
        else:
            self.parent.showMaximized()
            self.maximize_btn.setText("❐")
    
    def close_window(self):
        self.parent.close()
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.start_pos = event.globalPosition().toPoint()
    
    def mouseMoveEvent(self, event):
        if self.start_pos and event.buttons() == Qt.LeftButton:
            delta = event.globalPosition().toPoint() - self.start_pos
            self.parent.move(self.parent.pos() + delta)
            self.start_pos = event.globalPosition().toPoint()

class FluentButton(QPushButton):
    def __init__(self, text, primary=False):
        super().__init__(text)
        self.primary = primary
        self.setFixedHeight(35)
        self.setFont(QFont("Segoe UI", 10))
        self.apply_style()
        
    def apply_style(self):
        if self.primary:
            self.setStyleSheet("""
                QPushButton {
                    background-color: #0078d4;
                    color: white;
                    border: 1px solid #0078d4;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                    border-color: #106ebe;
                }
                QPushButton:pressed {
                    background-color: #005a9e;
                    border-color: #005a9e;
                }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background-color: rgba(255, 255, 255, 10);
                    color: white;
                    border: 1px solid rgba(255, 255, 255, 30);
                    border-radius: 4px;
                    padding: 8px 16px;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 20);
                    border-color: rgba(255, 255, 255, 50);
                }
                QPushButton:pressed {
                    background-color: rgba(255, 255, 255, 30);
                }
            """)

class FluentCard(QFrame):
    def __init__(self, title="", content=""):
        super().__init__()
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 15);
                border: 1px solid rgba(255, 255, 255, 20);
                border-radius: 8px;
                margin: 5px;
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 15, 20, 15)
        
        if title:
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                    background: transparent;
                    margin-bottom: 10px;
                }
            """)
            layout.addWidget(title_label)
        
        if content:
            content_label = QLabel(content)
            content_label.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 180);
                    font-size: 12px;
                    background: transparent;
                    line-height: 1.4;
                }
            """)
            content_label.setWordWrap(True)
            layout.addWidget(content_label)
        
        self.setLayout(layout)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setMinimumSize(800, 600)
        self.resize(1000, 700)
        
        # 设置窗口居中
        self.center_window()
        
        # 创建主窗口部件
        self.setup_ui()
        
    def center_window(self):
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def setup_ui(self):
        # 主容器
        main_widget = QWidget()
        main_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(20, 20, 20, 240);
                border-radius: 10px;
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 5)
        main_widget.setGraphicsEffect(shadow)
        
        self.setCentralWidget(main_widget)
        
        # 主布局
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 标题栏
        self.title_bar = TitleBar(self)
        main_layout.addWidget(self.title_bar)
        
        # 内容区域
        content_widget = QWidget()
        content_widget.setStyleSheet("background: transparent;")
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(30, 20, 30, 30)
        content_layout.setSpacing(20)
        
        # 欢迎标题
        welcome_label = QLabel("欢迎使用 Fluent UI")
        welcome_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 28px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 10px;
            }
        """)
        content_layout.addWidget(welcome_label)
        
        # 副标题
        subtitle_label = QLabel("这是一个使用PySide6创建的现代化无边框界面")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 160);
                font-size: 14px;
                background: transparent;
                margin-bottom: 20px;
            }
        """)
        content_layout.addWidget(subtitle_label)
        
        # 卡片容器
        cards_layout = QHBoxLayout()
        
        # 创建示例卡片
        card1 = FluentCard("功能特性", "• 无边框设计\n• Fluent Design风格\n• 毛玻璃效果\n• 现代化控件")
        card2 = FluentCard("技术栈", "• PySide6\n• Qt框架\n• Python\n• 现代UI设计")
        card3 = FluentCard("特色", "• 自定义标题栏\n• 窗口拖拽\n• 阴影效果\n• 响应式布局")
        
        cards_layout.addWidget(card1)
        cards_layout.addWidget(card2)
        cards_layout.addWidget(card3)
        
        content_layout.addLayout(cards_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        primary_btn = FluentButton("主要操作", primary=True)
        secondary_btn = FluentButton("次要操作")
        
        button_layout.addWidget(secondary_btn)
        button_layout.addWidget(primary_btn)
        
        content_layout.addLayout(button_layout)
        content_layout.addStretch()
        
        main_layout.addWidget(content_widget)

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion样式作为基础
    
    # 设置应用程序图标和名称
    app.setApplicationName("Fluent UI App")
    app.setApplicationVersion("1.0")
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
