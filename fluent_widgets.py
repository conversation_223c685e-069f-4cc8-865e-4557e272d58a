from PySide6.QtWidgets import (QW<PERSON>t, QPushButton, QLabel, QFrame, QVBoxLayout, 
                               QHBoxLayout, QGraphicsDropShadowEffect, QLineEdit, QSlider)
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, Signal
from PySide6.QtGui import QPainter, QColor, QFont, QPalette, QBrush, QLinearGradient

class FluentLineEdit(QLineEdit):
    def __init__(self, placeholder=""):
        super().__init__()
        self.setPlaceholderText(placeholder)
        self.setFixedHeight(35)
        self.setFont(QFont("Segoe UI", 10))
        self.setStyleSheet("""
            QLineEdit {
                background-color: rgba(255, 255, 255, 10);
                border: 1px solid rgba(255, 255, 255, 30);
                border-radius: 4px;
                padding: 8px 12px;
                color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 2px solid #0078d4;
                background-color: rgba(255, 255, 255, 15);
            }
            QLineEdit::placeholder {
                color: rgba(255, 255, 255, 120);
            }
        """)

class FluentSlider(QSlider):
    def __init__(self, orientation=Qt.Horizontal):
        super().__init__(orientation)
        self.setStyleSheet("""
            QSlider::groove:horizontal {
                border: none;
                height: 4px;
                background: rgba(255, 255, 255, 30);
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                background: #0078d4;
                border: none;
                width: 16px;
                height: 16px;
                margin: -6px 0;
                border-radius: 8px;
            }
            QSlider::handle:horizontal:hover {
                background: #106ebe;
            }
            QSlider::sub-page:horizontal {
                background: #0078d4;
                border-radius: 2px;
            }
        """)

class FluentToggleButton(QPushButton):
    toggled = Signal(bool)
    
    def __init__(self, text=""):
        super().__init__(text)
        self.setCheckable(True)
        self.setFixedHeight(35)
        self.setFont(QFont("Segoe UI", 10))
        self.clicked.connect(self.on_clicked)
        self.update_style()
        
    def on_clicked(self):
        self.update_style()
        self.toggled.emit(self.isChecked())
        
    def update_style(self):
        if self.isChecked():
            self.setStyleSheet("""
                QPushButton {
                    background-color: #0078d4;
                    color: white;
                    border: 1px solid #0078d4;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background-color: rgba(255, 255, 255, 10);
                    color: white;
                    border: 1px solid rgba(255, 255, 255, 30);
                    border-radius: 4px;
                    padding: 8px 16px;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 20);
                }
            """)

class FluentProgressBar(QWidget):
    def __init__(self):
        super().__init__()
        self.setFixedHeight(4)
        self.progress = 0
        self.setStyleSheet("background: rgba(255, 255, 255, 30); border-radius: 2px;")
        
    def set_progress(self, value):
        self.progress = max(0, min(100, value))
        self.update()
        
    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制进度
        if self.progress > 0:
            progress_width = int(self.width() * self.progress / 100)
            painter.fillRect(0, 0, progress_width, self.height(), QColor("#0078d4"))

class FluentSeparator(QFrame):
    def __init__(self, orientation=Qt.Horizontal):
        super().__init__()
        if orientation == Qt.Horizontal:
            self.setFrameShape(QFrame.HLine)
            self.setFixedHeight(1)
        else:
            self.setFrameShape(QFrame.VLine)
            self.setFixedWidth(1)
        
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 20);
                border: none;
            }
        """)

class FluentInfoBar(QFrame):
    def __init__(self, message="", info_type="info"):
        super().__init__()
        self.setFrameStyle(QFrame.NoFrame)
        self.setFixedHeight(50)
        
        # 根据类型设置颜色
        colors = {
            "info": "#0078d4",
            "success": "#107c10", 
            "warning": "#ff8c00",
            "error": "#d13438"
        }
        
        color = colors.get(info_type, "#0078d4")
        
        self.setStyleSheet(f"""
            QFrame {{
                background-color: rgba({int(color[1:3], 16)}, {int(color[3:5], 16)}, {int(color[5:7], 16)}, 30);
                border-left: 3px solid {color};
                border-radius: 4px;
                margin: 5px 0;
            }}
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)
        
        message_label = QLabel(message)
        message_label.setStyleSheet(f"""
            QLabel {{
                color: white;
                font-size: 12px;
                background: transparent;
            }}
        """)
        
        layout.addWidget(message_label)
        layout.addStretch()
        
        self.setLayout(layout)

class FluentNavigationItem(QPushButton):
    def __init__(self, text, icon_text=""):
        super().__init__()
        self.text = text
        self.icon_text = icon_text
        self.setCheckable(True)
        self.setFixedHeight(40)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(15, 0, 15, 0)
        
        if icon_text:
            icon_label = QLabel(icon_text)
            icon_label.setFixedSize(20, 20)
            icon_label.setStyleSheet("color: white; font-size: 16px; background: transparent;")
            layout.addWidget(icon_label)
        
        text_label = QLabel(text)
        text_label.setStyleSheet("color: white; font-size: 12px; background: transparent;")
        layout.addWidget(text_label)
        layout.addStretch()
        
        self.setLayout(layout)
        self.update_style()
        
    def update_style(self):
        if self.isChecked():
            self.setStyleSheet("""
                QPushButton {
                    background-color: rgba(0, 120, 212, 50);
                    border: none;
                    border-radius: 4px;
                    text-align: left;
                }
                QPushButton:hover {
                    background-color: rgba(0, 120, 212, 70);
                }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    border: none;
                    border-radius: 4px;
                    text-align: left;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 10);
                }
            """)
    
    def setChecked(self, checked):
        super().setChecked(checked)
        self.update_style()

class FluentNavigationPanel(QFrame):
    def __init__(self):
        super().__init__()
        self.setFixedWidth(250)
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(30, 30, 30, 200);
                border: none;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 20, 10, 20)
        layout.setSpacing(5)
        
        # 标题
        title = QLabel("导航")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 15px;
                padding-left: 15px;
            }
        """)
        layout.addWidget(title)
        
        # 导航项
        self.nav_items = []
        nav_data = [
            ("🏠", "首页"),
            ("📊", "数据"),
            ("⚙️", "设置"),
            ("ℹ️", "关于")
        ]
        
        for icon, text in nav_data:
            item = FluentNavigationItem(text, icon)
            self.nav_items.append(item)
            layout.addWidget(item)
            item.clicked.connect(lambda checked, i=item: self.on_nav_clicked(i))
        
        # 默认选中第一项
        if self.nav_items:
            self.nav_items[0].setChecked(True)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def on_nav_clicked(self, clicked_item):
        # 取消其他项的选中状态
        for item in self.nav_items:
            if item != clicked_item:
                item.setChecked(False)
