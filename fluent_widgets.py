from PySide6.QtWidgets import (QWidget, QPushButton, QLabel, QFrame, QVBoxLayout,
                               QHBoxLayout, QGraphicsDropShadowEffect, QLineEdit, QSlider,
                               QCheckBox, QRadioButton, QComboBox, QTextEdit, QSpinBox,
                               QDateEdit, QTimeEdit, QButtonGroup, QGroupBox, QTabWidget,
                               QTabBar, QListWidget, QListWidgetItem, QTreeWidget, QTreeWidgetItem,
                               QTableWidget, QTableWidgetItem, QHeaderView, QSplitter)
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, Signal, QDate, QTime, QTimer
from PySide6.QtGui import QPainter, QColor, QFont, QPalette, QBrush, QLinearGradient, QPixmap

class FluentLineEdit(QLineEdit):
    def __init__(self, placeholder=""):
        super().__init__()
        self.setPlaceholderText(placeholder)
        self.setFixedHeight(35)
        self.setFont(QFont("Segoe UI", 10))
        self.setStyleSheet("""
            QLineEdit {
                background-color: rgba(255, 255, 255, 10);
                border: 1px solid rgba(255, 255, 255, 30);
                border-radius: 4px;
                padding: 8px 12px;
                color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 2px solid #0078d4;
                background-color: rgba(255, 255, 255, 15);
            }
            QLineEdit::placeholder {
                color: rgba(255, 255, 255, 120);
            }
        """)

class FluentSlider(QSlider):
    def __init__(self, orientation=Qt.Horizontal):
        super().__init__(orientation)
        self.setStyleSheet("""
            QSlider::groove:horizontal {
                border: none;
                height: 4px;
                background: rgba(255, 255, 255, 30);
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                background: #0078d4;
                border: none;
                width: 16px;
                height: 16px;
                margin: -6px 0;
                border-radius: 8px;
            }
            QSlider::handle:horizontal:hover {
                background: #106ebe;
            }
            QSlider::sub-page:horizontal {
                background: #0078d4;
                border-radius: 2px;
            }
        """)

class FluentToggleButton(QPushButton):
    toggled = Signal(bool)
    
    def __init__(self, text=""):
        super().__init__(text)
        self.setCheckable(True)
        self.setFixedHeight(35)
        self.setFont(QFont("Segoe UI", 10))
        self.clicked.connect(self.on_clicked)
        self.update_style()
        
    def on_clicked(self):
        self.update_style()
        self.toggled.emit(self.isChecked())
        
    def update_style(self):
        if self.isChecked():
            self.setStyleSheet("""
                QPushButton {
                    background-color: #0078d4;
                    color: white;
                    border: 1px solid #0078d4;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: 600;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background-color: rgba(255, 255, 255, 10);
                    color: white;
                    border: 1px solid rgba(255, 255, 255, 30);
                    border-radius: 4px;
                    padding: 8px 16px;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 20);
                }
            """)

class FluentProgressBar(QWidget):
    def __init__(self):
        super().__init__()
        self.setFixedHeight(4)
        self.progress = 0
        self.setStyleSheet("background: rgba(255, 255, 255, 30); border-radius: 2px;")
        
    def set_progress(self, value):
        self.progress = max(0, min(100, value))
        self.update()
        
    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制进度
        if self.progress > 0:
            progress_width = int(self.width() * self.progress / 100)
            painter.fillRect(0, 0, progress_width, self.height(), QColor("#0078d4"))

class FluentSeparator(QFrame):
    def __init__(self, orientation=Qt.Horizontal):
        super().__init__()
        if orientation == Qt.Horizontal:
            self.setFrameShape(QFrame.HLine)
            self.setFixedHeight(1)
        else:
            self.setFrameShape(QFrame.VLine)
            self.setFixedWidth(1)
        
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 20);
                border: none;
            }
        """)

class FluentInfoBar(QFrame):
    def __init__(self, message="", info_type="info"):
        super().__init__()
        self.setFrameStyle(QFrame.NoFrame)
        self.setFixedHeight(50)
        
        # 根据类型设置颜色
        colors = {
            "info": "#0078d4",
            "success": "#107c10", 
            "warning": "#ff8c00",
            "error": "#d13438"
        }
        
        color = colors.get(info_type, "#0078d4")
        
        self.setStyleSheet(f"""
            QFrame {{
                background-color: rgba({int(color[1:3], 16)}, {int(color[3:5], 16)}, {int(color[5:7], 16)}, 30);
                border-left: 3px solid {color};
                border-radius: 4px;
                margin: 5px 0;
            }}
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)

        self.message_label = QLabel(message)
        self.message_label.setStyleSheet(f"""
            QLabel {{
                color: white;
                font-size: 12px;
                background: transparent;
            }}
        """)

        layout.addWidget(self.message_label)
        layout.addStretch()

        self.setLayout(layout)

class FluentNavigationItem(QPushButton):
    def __init__(self, text, icon_text=""):
        super().__init__()
        self.text = text
        self.icon_text = icon_text
        self.setCheckable(True)
        self.setFixedHeight(40)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(15, 0, 15, 0)
        
        if icon_text:
            icon_label = QLabel(icon_text)
            icon_label.setFixedSize(20, 20)
            icon_label.setStyleSheet("color: white; font-size: 16px; background: transparent;")
            layout.addWidget(icon_label)
        
        text_label = QLabel(text)
        text_label.setStyleSheet("color: white; font-size: 12px; background: transparent;")
        layout.addWidget(text_label)
        layout.addStretch()
        
        self.setLayout(layout)
        self.update_style()
        
    def update_style(self):
        if self.isChecked():
            self.setStyleSheet("""
                QPushButton {
                    background-color: rgba(0, 120, 212, 50);
                    border: none;
                    border-radius: 4px;
                    text-align: left;
                }
                QPushButton:hover {
                    background-color: rgba(0, 120, 212, 70);
                }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    border: none;
                    border-radius: 4px;
                    text-align: left;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 10);
                }
            """)
    
    def setChecked(self, checked):
        super().setChecked(checked)
        self.update_style()

class FluentNavigationPanel(QFrame):
    def __init__(self):
        super().__init__()
        self.setFixedWidth(250)
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(30, 30, 30, 200);
                border: none;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 20, 10, 20)
        layout.setSpacing(5)
        
        # 标题
        title = QLabel("导航")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 15px;
                padding-left: 15px;
            }
        """)
        layout.addWidget(title)
        
        # 导航项
        self.nav_items = []
        nav_data = [
            ("🏠", "首页"),
            ("📊", "数据"),
            ("⚙️", "设置"),
            ("ℹ️", "关于")
        ]
        
        for icon, text in nav_data:
            item = FluentNavigationItem(text, icon)
            self.nav_items.append(item)
            layout.addWidget(item)
            item.clicked.connect(lambda checked, i=item: self.on_nav_clicked(i))
        
        # 默认选中第一项
        if self.nav_items:
            self.nav_items[0].setChecked(True)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def on_nav_clicked(self, clicked_item):
        # 取消其他项的选中状态
        for item in self.nav_items:
            if item != clicked_item:
                item.setChecked(False)

class FluentCheckBox(QCheckBox):
    def __init__(self, text=""):
        super().__init__(text)
        self.setFont(QFont("Segoe UI", 10))
        self.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 12px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid rgba(255, 255, 255, 50);
                border-radius: 3px;
                background-color: rgba(255, 255, 255, 10);
            }
            QCheckBox::indicator:hover {
                border-color: #0078d4;
                background-color: rgba(0, 120, 212, 20);
            }
            QCheckBox::indicator:checked {
                background-color: #0078d4;
                border-color: #0078d4;
            }
        """)

class FluentRadioButton(QRadioButton):
    def __init__(self, text=""):
        super().__init__(text)
        self.setFont(QFont("Segoe UI", 10))
        self.setStyleSheet("""
            QRadioButton {
                color: white;
                font-size: 12px;
                spacing: 8px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid rgba(255, 255, 255, 50);
                border-radius: 8px;
                background-color: rgba(255, 255, 255, 10);
            }
            QRadioButton::indicator:hover {
                border-color: #0078d4;
                background-color: rgba(0, 120, 212, 20);
            }
            QRadioButton::indicator:checked {
                background-color: #0078d4;
                border-color: #0078d4;
            }
        """)

class FluentComboBox(QComboBox):
    def __init__(self):
        super().__init__()
        self.setFont(QFont("Segoe UI", 10))
        self.setFixedHeight(35)
        self.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 10);
                border: 1px solid rgba(255, 255, 255, 30);
                border-radius: 4px;
                padding: 8px 12px;
                color: white;
                font-size: 12px;
                min-width: 120px;
            }
            QComboBox:hover {
                border-color: #0078d4;
                background-color: rgba(255, 255, 255, 15);
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox QAbstractItemView {
                background-color: rgba(30, 30, 30, 240);
                border: 1px solid rgba(255, 255, 255, 30);
                border-radius: 4px;
                color: white;
                selection-background-color: #0078d4;
                outline: none;
            }
        """)

class FluentTextEdit(QTextEdit):
    def __init__(self, placeholder=""):
        super().__init__()
        self.setPlaceholderText(placeholder)
        self.setFont(QFont("Segoe UI", 10))
        self.setStyleSheet("""
            QTextEdit {
                background-color: rgba(255, 255, 255, 10);
                border: 1px solid rgba(255, 255, 255, 30);
                border-radius: 4px;
                padding: 8px 12px;
                color: white;
                font-size: 12px;
            }
            QTextEdit:focus {
                border: 2px solid #0078d4;
                background-color: rgba(255, 255, 255, 15);
            }
        """)

class FluentSpinBox(QSpinBox):
    def __init__(self):
        super().__init__()
        self.setFont(QFont("Segoe UI", 10))
        self.setFixedHeight(35)
        self.setStyleSheet("""
            QSpinBox {
                background-color: rgba(255, 255, 255, 10);
                border: 1px solid rgba(255, 255, 255, 30);
                border-radius: 4px;
                padding: 8px 12px;
                color: white;
                font-size: 12px;
            }
            QSpinBox:focus {
                border: 2px solid #0078d4;
                background-color: rgba(255, 255, 255, 15);
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background-color: rgba(255, 255, 255, 20);
                border: none;
                width: 16px;
                border-radius: 2px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #0078d4;
            }
        """)

class FluentDateEdit(QDateEdit):
    def __init__(self):
        super().__init__()
        self.setDate(QDate.currentDate())
        self.setFont(QFont("Segoe UI", 10))
        self.setFixedHeight(35)
        self.setStyleSheet("""
            QDateEdit {
                background-color: rgba(255, 255, 255, 10);
                border: 1px solid rgba(255, 255, 255, 30);
                border-radius: 4px;
                padding: 8px 12px;
                color: white;
                font-size: 12px;
            }
            QDateEdit:focus {
                border: 2px solid #0078d4;
                background-color: rgba(255, 255, 255, 15);
            }
            QDateEdit::drop-down {
                border: none;
                width: 20px;
            }
        """)

class FluentTimeEdit(QTimeEdit):
    def __init__(self):
        super().__init__()
        self.setTime(QTime.currentTime())
        self.setFont(QFont("Segoe UI", 10))
        self.setFixedHeight(35)
        self.setStyleSheet("""
            QTimeEdit {
                background-color: rgba(255, 255, 255, 10);
                border: 1px solid rgba(255, 255, 255, 30);
                border-radius: 4px;
                padding: 8px 12px;
                color: white;
                font-size: 12px;
            }
            QTimeEdit:focus {
                border: 2px solid #0078d4;
                background-color: rgba(255, 255, 255, 15);
            }
        """)

class FluentRatingControl(QWidget):
    rating_changed = Signal(int)

    def __init__(self, max_rating=5):
        super().__init__()
        self.max_rating = max_rating
        self.current_rating = 0
        self.hover_rating = 0
        self.setFixedHeight(30)
        self.setMinimumWidth(max_rating * 25)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        star_size = 20
        spacing = 5

        for i in range(self.max_rating):
            x = i * (star_size + spacing)
            y = 5

            # 确定星星的状态
            if i < (self.hover_rating or self.current_rating):
                color = QColor("#ff8c00")  # 橙色填充
            else:
                color = QColor(255, 255, 255, 50)  # 灰色轮廓

            painter.setBrush(QBrush(color))
            painter.setPen(QColor(255, 255, 255, 100))

            # 绘制星星（简化为圆形）
            painter.drawEllipse(x, y, star_size, star_size)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            star_size = 20
            spacing = 5
            clicked_star = int(event.position().x() // (star_size + spacing)) + 1
            if 1 <= clicked_star <= self.max_rating:
                self.current_rating = clicked_star
                self.rating_changed.emit(self.current_rating)
                self.update()

    def mouseMoveEvent(self, event):
        star_size = 20
        spacing = 5
        hover_star = int(event.position().x() // (star_size + spacing)) + 1
        if 1 <= hover_star <= self.max_rating:
            self.hover_rating = hover_star
        else:
            self.hover_rating = 0
        self.update()

    def leaveEvent(self, event):
        self.hover_rating = 0
        self.update()

class FluentColorPicker(QPushButton):
    color_changed = Signal(QColor)

    def __init__(self, initial_color=QColor("#0078d4")):
        super().__init__()
        self.current_color = initial_color
        self.setFixedSize(80, 35)
        self.clicked.connect(self.pick_color)
        self.update_style()

    def update_style(self):
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.current_color.name()};
                border: 2px solid rgba(255, 255, 255, 30);
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border-color: rgba(255, 255, 255, 60);
            }}
        """)

    def pick_color(self):
        from PySide6.QtWidgets import QColorDialog
        color = QColorDialog.getColor(self.current_color, self, "选择颜色")
        if color.isValid():
            self.current_color = color
            self.update_style()
            self.color_changed.emit(color)

class FluentTabWidget(QTabWidget):
    def __init__(self):
        super().__init__()
        self.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid rgba(255, 255, 255, 20);
                border-radius: 4px;
                background-color: rgba(255, 255, 255, 5);
            }
            QTabBar::tab {
                background-color: rgba(255, 255, 255, 10);
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-size: 12px;
            }
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
            QTabBar::tab:hover {
                background-color: rgba(0, 120, 212, 50);
            }
        """)
