import sys
from PySide6.QtWidgets import (Q<PERSON><PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QPushButton, QLabel, QFrame, QGraphicsDropShadowEffect,
                               QScrollArea, QGridLayout, QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt, QPoint, QPropertyAnimation, QEasingCurve, QRect, QTimer
from PySide6.QtGui import QPainter, QColor, QFont, QIcon, QPalette, QPixmap

from fluent_widgets import (FluentLineEdit, FluentSlider, FluentToggleButton, 
                           FluentProgressBar, FluentSeparator, FluentInfoBar,
                           FluentNavigationPanel)

class TitleBar(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setFixedHeight(40)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(32, 32, 32, 200);
                border: none;
            }
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(10, 0, 10, 0)
        layout.setSpacing(0)
        
        self.title_label = QLabel("Fluent Design Demo")
        self.title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
        """)
        
        self.minimize_btn = self.create_title_button("−", self.minimize_window)
        self.maximize_btn = self.create_title_button("□", self.maximize_window)
        self.close_btn = self.create_title_button("×", self.close_window)
        self.close_btn.setStyleSheet(self.close_btn.styleSheet() + """
            QPushButton:hover {
                background-color: #e81123;
            }
        """)
        
        layout.addWidget(self.title_label)
        layout.addStretch()
        layout.addWidget(self.minimize_btn)
        layout.addWidget(self.maximize_btn)
        layout.addWidget(self.close_btn)
        
        self.setLayout(layout)
        self.start_pos = None
        
    def create_title_button(self, text, callback):
        btn = QPushButton(text)
        btn.setFixedSize(45, 30)
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                border: none;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 30);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 50);
            }
        """)
        btn.clicked.connect(callback)
        return btn
    
    def minimize_window(self):
        self.parent.showMinimized()
    
    def maximize_window(self):
        if self.parent.isMaximized():
            self.parent.showNormal()
            self.maximize_btn.setText("□")
        else:
            self.parent.showMaximized()
            self.maximize_btn.setText("❐")
    
    def close_window(self):
        self.parent.close()
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.start_pos = event.globalPosition().toPoint()
    
    def mouseMoveEvent(self, event):
        if self.start_pos and event.buttons() == Qt.LeftButton:
            delta = event.globalPosition().toPoint() - self.start_pos
            self.parent.move(self.parent.pos() + delta)
            self.start_pos = event.globalPosition().toPoint()

class ContentArea(QWidget):
    def __init__(self):
        super().__init__()
        self.setStyleSheet("background: transparent;")
        self.current_page = 0
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 20, 30, 30)
        layout.setSpacing(20)
        
        # 页面标题
        self.page_title = QLabel("首页")
        self.page_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(self.page_title)
        
        # 滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 10);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 50);
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(255, 255, 255, 70);
            }
        """)
        
        # 内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("background: transparent;")
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        
        # 创建不同页面的内容
        self.create_home_content(content_layout)
        
        scroll.setWidget(content_widget)
        layout.addWidget(scroll)
        
        self.setLayout(layout)
    
    def create_home_content(self, layout):
        # 信息栏
        info_bar = FluentInfoBar("欢迎使用 Fluent Design 演示应用！", "info")
        layout.addWidget(info_bar)
        
        # 控件演示区域
        demo_frame = QFrame()
        demo_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 8);
                border: 1px solid rgba(255, 255, 255, 15);
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        demo_layout = QGridLayout(demo_frame)
        demo_layout.setSpacing(20)
        
        # 输入框演示
        layout.addWidget(QLabel("文本输入:"))
        line_edit = FluentLineEdit("请输入文本...")
        demo_layout.addWidget(QLabel("文本输入:"), 0, 0)
        demo_layout.addWidget(line_edit, 0, 1)
        
        # 滑块演示
        slider = FluentSlider()
        slider.setRange(0, 100)
        slider.setValue(50)
        demo_layout.addWidget(QLabel("滑块控件:"), 1, 0)
        demo_layout.addWidget(slider, 1, 1)
        
        # 切换按钮演示
        toggle_btn = FluentToggleButton("切换按钮")
        demo_layout.addWidget(QLabel("切换按钮:"), 2, 0)
        demo_layout.addWidget(toggle_btn, 2, 1)
        
        # 进度条演示
        self.progress_bar = FluentProgressBar()
        demo_layout.addWidget(QLabel("进度条:"), 3, 0)
        demo_layout.addWidget(self.progress_bar, 3, 1)
        
        # 启动进度条动画
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.update_progress)
        self.progress_value = 0
        self.progress_timer.start(100)
        
        layout.addWidget(demo_frame)
        
        # 分隔符
        layout.addWidget(FluentSeparator())
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        primary_btn = QPushButton("主要按钮")
        primary_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        
        secondary_btn = QPushButton("次要按钮")
        secondary_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 10);
                color: white;
                border: 1px solid rgba(255, 255, 255, 30);
                border-radius: 4px;
                padding: 10px 20px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 20);
            }
        """)
        
        button_layout.addWidget(primary_btn)
        button_layout.addWidget(secondary_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 添加一些示例卡片
        cards_layout = QHBoxLayout()
        
        for i in range(3):
            card = self.create_feature_card(
                f"功能 {i+1}",
                f"这是功能 {i+1} 的详细描述，展示了 Fluent Design 的设计理念。"
            )
            cards_layout.addWidget(card)
        
        layout.addLayout(cards_layout)
        
        layout.addStretch()
    
    def create_feature_card(self, title, description):
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 12);
                border: 1px solid rgba(255, 255, 255, 20);
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        # 添加阴影
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 2)
        card.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout(card)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 8px;
            }
        """)
        
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 160);
                font-size: 11px;
                background: transparent;
                line-height: 1.4;
            }
        """)
        desc_label.setWordWrap(True)
        
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        return card
    
    def update_progress(self):
        self.progress_value = (self.progress_value + 2) % 101
        self.progress_bar.set_progress(self.progress_value)
    
    def switch_page(self, page_index):
        self.current_page = page_index
        pages = ["首页", "数据", "设置", "关于"]
        if page_index < len(pages):
            self.page_title.setText(pages[page_index])

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        self.center_window()
        self.setup_ui()
        
    def center_window(self):
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def setup_ui(self):
        main_widget = QWidget()
        main_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(20, 20, 20, 240);
                border-radius: 10px;
            }
        """)
        
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 5)
        main_widget.setGraphicsEffect(shadow)
        
        self.setCentralWidget(main_widget)
        
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 标题栏
        self.title_bar = TitleBar(self)
        main_layout.addWidget(self.title_bar)
        
        # 主内容区域
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # 导航面板
        self.nav_panel = FluentNavigationPanel()
        content_layout.addWidget(self.nav_panel)
        
        # 内容区域
        self.content_area = ContentArea()
        content_layout.addWidget(self.content_area)
        
        content_widget = QWidget()
        content_widget.setLayout(content_layout)
        main_layout.addWidget(content_widget)
        
        # 连接导航事件
        for i, item in enumerate(self.nav_panel.nav_items):
            item.clicked.connect(lambda checked, idx=i: self.content_area.switch_page(idx))

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    app.setApplicationName("Fluent Design Demo")
    app.setApplicationVersion("1.0")
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
