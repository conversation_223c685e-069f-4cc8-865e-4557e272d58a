#!/usr/bin/env python3
"""
Fluent Design UI Demo Launcher
运行这个脚本来启动演示应用
"""

import sys
import os

def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import PySide6
        print(f"✓ PySide6 {PySide6.__version__} 已安装")
        return True
    except ImportError:
        print("✗ PySide6 未安装")
        print("请运行: pip install PySide6")
        return False

def main():
    print("=" * 50)
    print("Fluent Design UI Demo")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    print("\n选择要运行的演示:")
    print("1. 基础版本 (main.py)")
    print("2. 标准演示版本 (fluent_app.py)")
    print("3. 增强版本 - 主题+国际化 (enhanced_fluent_app.py) [推荐]")
    print("4. 简单测试版本 (test_ui.py)")
    print("5. 退出")

    while True:
        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == "1":
            print("\n启动基础版本...")
            os.system(f"{sys.executable} main.py")
            break
        elif choice == "2":
            print("\n启动标准演示版本...")
            os.system(f"{sys.executable} fluent_app.py")
            break
        elif choice == "3":
            print("\n启动增强版本（推荐）...")
            os.system(f"{sys.executable} enhanced_fluent_app.py")
            break
        elif choice == "4":
            print("\n启动简单测试版本...")
            os.system(f"{sys.executable} test_ui.py")
            break
        elif choice == "5":
            print("退出")
            break
        else:
            print("无效选择，请输入 1-5")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
