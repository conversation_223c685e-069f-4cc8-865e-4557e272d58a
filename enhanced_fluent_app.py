import sys
import platform
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QPushButton, QLabel, QFrame, QGraphicsDropShadowEffect,
                               QScrollArea, QGridLayout, QGroupBox, QButtonGroup)
from PySide6.QtCore import Qt, QPoint, QPropertyAnimation, QEasingCurve, QRect, QTimer
from PySide6.QtGui import QPainter, QColor, QFont, QIcon, QPalette, QPixmap

from theme_manager import theme_manager, ThemeType
from i18n_manager import i18n_manager, Language
from fluent_widgets import (FluentLineEdit, FluentSlider, FluentToggleButton,
                           FluentProgressBar, FluentSeparator, FluentInfoBar,
                           FluentNavigationPanel, FluentNavigationItem, FluentCheckBox, FluentRadioButton,
                           FluentComboBox, FluentTextEdit, FluentSpinBox,
                           FluentDateEdit, FluentTimeEdit, FluentRatingControl,
                           FluentColorPicker, FluentTabWidget)

class TitleBar(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setFixedHeight(40)
        self.setup_ui()
        
        # 连接主题变化信号
        theme_manager.theme_changed.connect(self.update_theme)
        i18n_manager.language_changed.connect(self.update_language)
        
        self.start_pos = None
        
    def setup_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(10, 0, 10, 0)
        layout.setSpacing(0)
        
        self.title_label = QLabel()
        self.update_language()
        
        self.minimize_btn = self.create_title_button("−", self.minimize_window)
        self.maximize_btn = self.create_title_button("□", self.maximize_window)
        self.close_btn = self.create_title_button("×", self.close_window)
        
        layout.addWidget(self.title_label)
        layout.addStretch()
        layout.addWidget(self.minimize_btn)
        layout.addWidget(self.maximize_btn)
        layout.addWidget(self.close_btn)
        
        self.setLayout(layout)
        self.update_theme()
        
    def create_title_button(self, text, callback):
        btn = QPushButton(text)
        btn.setFixedSize(45, 30)
        btn.clicked.connect(callback)
        return btn
    
    def update_theme(self):
        theme = theme_manager.get_current_theme()
        self.setStyleSheet(theme_manager.get_style_sheet("title_bar"))
        
        # 更新标题标签样式
        self.title_label.setStyleSheet(f"""
            QLabel {{
                color: {theme['text_color']};
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }}
        """)
        
        # 更新按钮样式
        button_style = f"""
            QPushButton {{
                background-color: transparent;
                color: {theme['text_color']};
                border: none;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {theme['hover_color']};
            }}
            QPushButton:pressed {{
                background-color: {theme['pressed_color']};
            }}
        """
        
        self.minimize_btn.setStyleSheet(button_style)
        self.maximize_btn.setStyleSheet(button_style)
        
        # 关闭按钮特殊样式
        close_style = button_style + """
            QPushButton:hover {
                background-color: #e81123;
            }
        """
        self.close_btn.setStyleSheet(close_style)
    
    def update_language(self):
        self.title_label.setText(i18n_manager.get_text("app_title"))
    
    def minimize_window(self):
        self.parent.showMinimized()
    
    def maximize_window(self):
        if self.parent.isMaximized():
            self.parent.showNormal()
            self.maximize_btn.setText("□")
        else:
            self.parent.showMaximized()
            self.maximize_btn.setText("❐")
    
    def close_window(self):
        self.parent.close()
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.start_pos = event.globalPosition().toPoint()
    
    def mouseMoveEvent(self, event):
        if self.start_pos and event.buttons() == Qt.LeftButton:
            delta = event.globalPosition().toPoint() - self.start_pos
            self.parent.move(self.parent.pos() + delta)
            self.start_pos = event.globalPosition().toPoint()

class HomePage(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        theme_manager.theme_changed.connect(self.update_theme)
        i18n_manager.language_changed.connect(self.update_language)
        
    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 20, 30, 30)
        layout.setSpacing(20)
        
        # 页面标题
        self.page_title = QLabel()
        layout.addWidget(self.page_title)
        
        # 欢迎信息
        self.info_bar = FluentInfoBar()
        layout.addWidget(self.info_bar)
        
        # 功能卡片
        cards_layout = QHBoxLayout()
        
        self.feature_cards = []
        for i in range(3):
            card = self.create_feature_card(i)
            self.feature_cards.append(card)
            cards_layout.addWidget(card)
        
        layout.addLayout(cards_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.primary_btn = QPushButton()
        self.secondary_btn = QPushButton()
        
        button_layout.addWidget(self.secondary_btn)
        button_layout.addWidget(self.primary_btn)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        self.setLayout(layout)
        self.update_language()
        self.update_theme()
    
    def create_feature_card(self, index):
        card = QFrame()
        card.setFrameStyle(QFrame.NoFrame)
        
        # 添加阴影
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 2)
        card.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout(card)
        
        title_label = QLabel()
        title_label.setObjectName(f"feature_title_{index}")
        
        desc_label = QLabel()
        desc_label.setObjectName(f"feature_desc_{index}")
        desc_label.setWordWrap(True)
        
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        return card
    
    def update_theme(self):
        theme = theme_manager.get_current_theme()
        
        # 更新页面标题样式
        self.page_title.setStyleSheet(f"""
            QLabel {{
                color: {theme['text_color']};
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 10px;
            }}
        """)
        
        # 更新按钮样式
        self.primary_btn.setStyleSheet(theme_manager.get_style_sheet("button_primary"))
        self.secondary_btn.setStyleSheet(theme_manager.get_style_sheet("button_secondary"))
        
        # 更新功能卡片样式
        card_style = theme_manager.get_style_sheet("card")
        for card in self.feature_cards:
            card.setStyleSheet(card_style)
            
            # 更新卡片内的标签样式
            for child in card.findChildren(QLabel):
                if "title" in child.objectName():
                    child.setStyleSheet(f"""
                        QLabel {{
                            color: {theme['text_color']};
                            font-size: 14px;
                            font-weight: bold;
                            background: transparent;
                            margin-bottom: 8px;
                        }}
                    """)
                elif "desc" in child.objectName():
                    child.setStyleSheet(f"""
                        QLabel {{
                            color: {theme['text_secondary']};
                            font-size: 11px;
                            background: transparent;
                            line-height: 1.4;
                        }}
                    """)
    
    def update_language(self):
        self.page_title.setText(i18n_manager.get_text("home_title"))
        self.info_bar.message_label.setText(i18n_manager.get_text("home_welcome"))
        self.primary_btn.setText(i18n_manager.get_text("primary_action"))
        self.secondary_btn.setText(i18n_manager.get_text("secondary_action"))
        
        # 更新功能卡片文本
        features = [
            ("feature_1", "feature_1_desc"),
            ("feature_2", "feature_2_desc"),
            ("feature_3", "feature_3_desc")
        ]
        
        for i, (title_key, desc_key) in enumerate(features):
            if i < len(self.feature_cards):
                card = self.feature_cards[i]
                title_label = card.findChild(QLabel, f"feature_title_{i}")
                desc_label = card.findChild(QLabel, f"feature_desc_{i}")
                
                if title_label:
                    title_label.setText(i18n_manager.get_text(title_key))
                if desc_label:
                    desc_label.setText(i18n_manager.get_text(desc_key))

class ComponentsPage(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        theme_manager.theme_changed.connect(self.update_theme)
        i18n_manager.language_changed.connect(self.update_language)

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 20, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        self.page_title = QLabel()
        layout.addWidget(self.page_title)

        # 描述
        self.description = QLabel()
        layout.addWidget(self.description)

        # 滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("QScrollArea { border: none; background: transparent; }")

        # 组件演示容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(30)

        # 输入控件组
        self.create_input_controls_group(content_layout)

        # 选择控件组
        self.create_selection_controls_group(content_layout)

        # 显示控件组
        self.create_display_controls_group(content_layout)

        content_layout.addStretch()
        scroll.setWidget(content_widget)
        layout.addWidget(scroll)

        self.setLayout(layout)
        self.update_language()
        self.update_theme()

    def create_input_controls_group(self, parent_layout):
        group = QGroupBox()
        group.setObjectName("input_group")
        layout = QGridLayout(group)
        layout.setSpacing(15)

        # 文本输入
        layout.addWidget(QLabel("text_input"), 0, 0)
        self.text_input = FluentLineEdit()
        layout.addWidget(self.text_input, 0, 1)

        # 密码输入
        layout.addWidget(QLabel("password_input"), 1, 0)
        self.password_input = FluentLineEdit()
        self.password_input.setEchoMode(FluentLineEdit.Password)
        layout.addWidget(self.password_input, 1, 1)

        # 数字输入
        layout.addWidget(QLabel("number_input"), 2, 0)
        self.number_input = FluentSpinBox()
        self.number_input.setRange(0, 100)
        layout.addWidget(self.number_input, 2, 1)

        # 多行输入
        layout.addWidget(QLabel("multiline_input"), 3, 0)
        self.multiline_input = FluentTextEdit()
        self.multiline_input.setMaximumHeight(80)
        layout.addWidget(self.multiline_input, 3, 1)

        parent_layout.addWidget(group)

    def create_selection_controls_group(self, parent_layout):
        group = QGroupBox()
        group.setObjectName("selection_group")
        layout = QGridLayout(group)
        layout.setSpacing(15)

        # 复选框
        layout.addWidget(QLabel("checkbox"), 0, 0)
        checkbox_layout = QHBoxLayout()
        self.checkbox1 = FluentCheckBox("选项 1")
        self.checkbox2 = FluentCheckBox("选项 2")
        checkbox_layout.addWidget(self.checkbox1)
        checkbox_layout.addWidget(self.checkbox2)
        checkbox_layout.addStretch()
        layout.addLayout(checkbox_layout, 0, 1)

        # 单选按钮
        layout.addWidget(QLabel("radio_button"), 1, 0)
        radio_layout = QHBoxLayout()
        self.radio_group = QButtonGroup()
        self.radio1 = FluentRadioButton("选项 A")
        self.radio2 = FluentRadioButton("选项 B")
        self.radio_group.addButton(self.radio1)
        self.radio_group.addButton(self.radio2)
        radio_layout.addWidget(self.radio1)
        radio_layout.addWidget(self.radio2)
        radio_layout.addStretch()
        layout.addLayout(radio_layout, 1, 1)

        # 下拉框
        layout.addWidget(QLabel("combo_box"), 2, 0)
        self.combo_box = FluentComboBox()
        self.combo_box.addItems(["选项 1", "选项 2", "选项 3"])
        layout.addWidget(self.combo_box, 2, 1)

        # 滑块
        layout.addWidget(QLabel("slider_control"), 3, 0)
        self.slider = FluentSlider()
        self.slider.setRange(0, 100)
        self.slider.setValue(50)
        layout.addWidget(self.slider, 3, 1)

        parent_layout.addWidget(group)

    def create_display_controls_group(self, parent_layout):
        group = QGroupBox()
        group.setObjectName("display_group")
        layout = QGridLayout(group)
        layout.setSpacing(15)

        # 进度条
        layout.addWidget(QLabel("progress_bar"), 0, 0)
        self.progress_bar = FluentProgressBar()
        self.progress_bar.set_progress(65)
        layout.addWidget(self.progress_bar, 0, 1)

        # 评分控件
        layout.addWidget(QLabel("rating_control"), 1, 0)
        self.rating = FluentRatingControl()
        layout.addWidget(self.rating, 1, 1)

        # 颜色选择器
        layout.addWidget(QLabel("color_picker"), 2, 0)
        self.color_picker = FluentColorPicker()
        layout.addWidget(self.color_picker, 2, 1)

        # 日期时间
        layout.addWidget(QLabel("date_picker"), 3, 0)
        datetime_layout = QHBoxLayout()
        self.date_edit = FluentDateEdit()
        self.time_edit = FluentTimeEdit()
        datetime_layout.addWidget(self.date_edit)
        datetime_layout.addWidget(self.time_edit)
        datetime_layout.addStretch()
        layout.addLayout(datetime_layout, 3, 1)

        parent_layout.addWidget(group)

    def update_theme(self):
        theme = theme_manager.get_current_theme()

        # 更新页面标题样式
        self.page_title.setStyleSheet(f"""
            QLabel {{
                color: {theme['text_color']};
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 10px;
            }}
        """)

        # 更新描述样式
        self.description.setStyleSheet(f"""
            QLabel {{
                color: {theme['text_secondary']};
                font-size: 14px;
                background: transparent;
                margin-bottom: 20px;
            }}
        """)

        # 更新组框样式
        group_style = f"""
            QGroupBox {{
                background-color: {theme['card_color']};
                border: 1px solid {theme['border_color']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                font-weight: bold;
                color: {theme['text_color']};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """

        for group in self.findChildren(QGroupBox):
            group.setStyleSheet(group_style)

        # 更新标签样式
        label_style = f"""
            QLabel {{
                color: {theme['text_color']};
                font-size: 12px;
                background: transparent;
            }}
        """

        for label in self.findChildren(QLabel):
            if not label.objectName() in ["page_title", "description"]:
                label.setStyleSheet(label_style)

    def update_language(self):
        self.page_title.setText(i18n_manager.get_text("components_title"))
        self.description.setText(i18n_manager.get_text("components_description"))

        # 更新组框标题
        groups = self.findChildren(QGroupBox)
        group_titles = ["input_controls", "selection_controls", "display_controls"]
        for i, group in enumerate(groups):
            if i < len(group_titles):
                group.setTitle(i18n_manager.get_text(group_titles[i]))

        # 更新占位符文本
        self.text_input.setPlaceholderText(i18n_manager.get_text("placeholder_text"))
        self.password_input.setPlaceholderText(i18n_manager.get_text("placeholder_password"))
        self.multiline_input.setPlaceholderText(i18n_manager.get_text("placeholder_text"))

class ThemePage(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        theme_manager.theme_changed.connect(self.update_theme)
        i18n_manager.language_changed.connect(self.update_language)

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 20, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        self.page_title = QLabel()
        layout.addWidget(self.page_title)

        # 描述
        self.description = QLabel()
        layout.addWidget(self.description)

        # 主题选择区域
        themes_layout = QGridLayout()
        themes_layout.setSpacing(15)

        self.theme_buttons = {}
        theme_names = theme_manager.get_theme_names()

        row, col = 0, 0
        for theme_type, theme_name in theme_names.items():
            btn = self.create_theme_button(theme_type, theme_name)
            self.theme_buttons[theme_type] = btn
            themes_layout.addWidget(btn, row, col)

            col += 1
            if col >= 3:  # 每行3个主题
                col = 0
                row += 1

        layout.addLayout(themes_layout)
        layout.addStretch()

        self.setLayout(layout)
        self.update_language()
        self.update_theme()

    def create_theme_button(self, theme_type, theme_name):
        btn = QPushButton()
        btn.setFixedSize(200, 120)
        btn.clicked.connect(lambda: self.apply_theme(theme_type))

        # 创建主题预览
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setContentsMargins(10, 10, 10, 10)

        # 主题名称
        name_label = QLabel(theme_name)
        name_label.setAlignment(Qt.AlignCenter)
        preview_layout.addWidget(name_label)

        # 颜色预览
        colors_layout = QHBoxLayout()
        theme_data = theme_manager.themes[theme_type]

        for color_key in ["primary_color", "secondary_color", "accent_color"]:
            color_preview = QFrame()
            color_preview.setFixedSize(30, 20)
            color_preview.setStyleSheet(f"""
                QFrame {{
                    background-color: {theme_data[color_key]};
                    border-radius: 4px;
                }}
            """)
            colors_layout.addWidget(color_preview)

        preview_layout.addLayout(colors_layout)

        # 将预览添加到按钮
        btn_layout = QVBoxLayout(btn)
        btn_layout.addWidget(preview_widget)

        return btn

    def apply_theme(self, theme_type):
        theme_manager.set_theme(theme_type)
        self.update_current_theme_indicator()

    def update_current_theme_indicator(self):
        current_theme = theme_manager.current_theme
        for theme_type, btn in self.theme_buttons.items():
            if theme_type == current_theme:
                btn.setStyleSheet("""
                    QPushButton {
                        border: 3px solid #0078d4;
                        border-radius: 8px;
                        background-color: rgba(0, 120, 212, 20);
                    }
                """)
            else:
                btn.setStyleSheet("""
                    QPushButton {
                        border: 1px solid rgba(255, 255, 255, 30);
                        border-radius: 8px;
                        background-color: rgba(255, 255, 255, 5);
                    }
                    QPushButton:hover {
                        border-color: rgba(255, 255, 255, 60);
                        background-color: rgba(255, 255, 255, 10);
                    }
                """)

    def update_theme(self):
        theme = theme_manager.get_current_theme()

        # 更新页面标题样式
        self.page_title.setStyleSheet(f"""
            QLabel {{
                color: {theme['text_color']};
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 10px;
            }}
        """)

        # 更新描述样式
        self.description.setStyleSheet(f"""
            QLabel {{
                color: {theme['text_secondary']};
                font-size: 14px;
                background: transparent;
                margin-bottom: 20px;
            }}
        """)

        self.update_current_theme_indicator()

    def update_language(self):
        self.page_title.setText(i18n_manager.get_text("themes_title"))
        self.description.setText(i18n_manager.get_text("themes_description"))

class SettingsPage(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        theme_manager.theme_changed.connect(self.update_theme)
        i18n_manager.language_changed.connect(self.update_language)

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 20, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        self.page_title = QLabel()
        layout.addWidget(self.page_title)

        # 描述
        self.description = QLabel()
        layout.addWidget(self.description)

        # 设置组
        self.create_language_settings(layout)
        self.create_appearance_settings(layout)
        self.create_behavior_settings(layout)

        layout.addStretch()
        self.setLayout(layout)
        self.update_language()
        self.update_theme()

    def create_language_settings(self, parent_layout):
        group = QGroupBox()
        group.setObjectName("language_group")
        layout = QGridLayout(group)
        layout.setSpacing(15)

        # 语言选择
        layout.addWidget(QLabel("language"), 0, 0)
        self.language_combo = FluentComboBox()
        languages = i18n_manager.get_available_languages()
        for lang, name in languages.items():
            self.language_combo.addItem(name, lang)

        # 设置当前语言
        current_lang = i18n_manager.get_current_language()
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == current_lang:
                self.language_combo.setCurrentIndex(i)
                break

        self.language_combo.currentIndexChanged.connect(self.change_language)
        layout.addWidget(self.language_combo, 0, 1)

        parent_layout.addWidget(group)

    def create_appearance_settings(self, parent_layout):
        group = QGroupBox()
        group.setObjectName("appearance_group")
        layout = QGridLayout(group)
        layout.setSpacing(15)

        # 主题选择
        layout.addWidget(QLabel("theme"), 0, 0)
        self.theme_combo = FluentComboBox()
        themes = theme_manager.get_theme_names()
        for theme_type, name in themes.items():
            self.theme_combo.addItem(name, theme_type)

        # 设置当前主题
        current_theme = theme_manager.current_theme
        for i in range(self.theme_combo.count()):
            if self.theme_combo.itemData(i) == current_theme:
                self.theme_combo.setCurrentIndex(i)
                break

        self.theme_combo.currentIndexChanged.connect(self.change_theme)
        layout.addWidget(self.theme_combo, 0, 1)

        parent_layout.addWidget(group)

    def create_behavior_settings(self, parent_layout):
        group = QGroupBox()
        group.setObjectName("behavior_group")
        layout = QGridLayout(group)
        layout.setSpacing(15)

        # 行为设置
        self.auto_start_cb = FluentCheckBox()
        layout.addWidget(self.auto_start_cb, 0, 0, 1, 2)

        self.minimize_to_tray_cb = FluentCheckBox()
        layout.addWidget(self.minimize_to_tray_cb, 1, 0, 1, 2)

        self.enable_animations_cb = FluentCheckBox()
        self.enable_animations_cb.setChecked(True)
        layout.addWidget(self.enable_animations_cb, 2, 0, 1, 2)

        self.enable_sounds_cb = FluentCheckBox()
        layout.addWidget(self.enable_sounds_cb, 3, 0, 1, 2)

        parent_layout.addWidget(group)

    def change_language(self, index):
        lang = self.language_combo.itemData(index)
        if lang:
            i18n_manager.set_language(lang)

    def change_theme(self, index):
        theme_type = self.theme_combo.itemData(index)
        if theme_type:
            theme_manager.set_theme(theme_type)

    def update_theme(self):
        theme = theme_manager.get_current_theme()

        # 更新页面标题样式
        self.page_title.setStyleSheet(f"""
            QLabel {{
                color: {theme['text_color']};
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 10px;
            }}
        """)

        # 更新描述样式
        self.description.setStyleSheet(f"""
            QLabel {{
                color: {theme['text_secondary']};
                font-size: 14px;
                background: transparent;
                margin-bottom: 20px;
            }}
        """)

        # 更新组框样式
        group_style = f"""
            QGroupBox {{
                background-color: {theme['card_color']};
                border: 1px solid {theme['border_color']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                font-weight: bold;
                color: {theme['text_color']};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """

        for group in self.findChildren(QGroupBox):
            group.setStyleSheet(group_style)

    def update_language(self):
        self.page_title.setText(i18n_manager.get_text("settings_title"))
        self.description.setText(i18n_manager.get_text("settings_description"))

        # 更新组框标题
        groups = self.findChildren(QGroupBox)
        group_titles = ["language_settings", "appearance_settings", "behavior_settings"]
        for i, group in enumerate(groups):
            if i < len(group_titles):
                group.setTitle(i18n_manager.get_text(group_titles[i]))

        # 更新复选框文本
        self.auto_start_cb.setText(i18n_manager.get_text("auto_start"))
        self.minimize_to_tray_cb.setText(i18n_manager.get_text("minimize_to_tray"))
        self.enable_animations_cb.setText(i18n_manager.get_text("enable_animations"))
        self.enable_sounds_cb.setText(i18n_manager.get_text("enable_sounds"))

class AboutPage(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        theme_manager.theme_changed.connect(self.update_theme)
        i18n_manager.language_changed.connect(self.update_language)

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 20, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        self.page_title = QLabel()
        layout.addWidget(self.page_title)

        # 应用信息卡片
        info_card = QFrame()
        info_card.setFrameStyle(QFrame.NoFrame)
        info_layout = QVBoxLayout(info_card)
        info_layout.setSpacing(15)

        # 应用名称
        self.app_name = QLabel()
        self.app_name.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(self.app_name)

        # 版本信息
        self.app_version = QLabel()
        self.app_version.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(self.app_version)

        # 作者信息
        self.app_author = QLabel()
        self.app_author.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(self.app_author)

        # 分隔符
        separator = FluentSeparator()
        info_layout.addWidget(separator)

        # 应用描述
        self.app_description = QLabel()
        self.app_description.setWordWrap(True)
        self.app_description.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(self.app_description)

        layout.addWidget(info_card)

        # 技术信息
        tech_group = QGroupBox()
        tech_group.setObjectName("tech_group")
        tech_layout = QGridLayout(tech_group)
        tech_layout.setSpacing(10)

        # 框架信息
        tech_layout.addWidget(QLabel("framework"), 0, 0)
        self.framework_label = QLabel("PySide6")
        tech_layout.addWidget(self.framework_label, 0, 1)

        # Python版本
        tech_layout.addWidget(QLabel("python_version"), 1, 0)
        python_version = f"Python {platform.python_version()}"
        self.python_version_label = QLabel(python_version)
        tech_layout.addWidget(self.python_version_label, 1, 1)

        # Qt版本
        tech_layout.addWidget(QLabel("qt_version"), 2, 0)
        from PySide6 import __version__ as pyside_version
        qt_version = f"Qt {pyside_version}"
        self.qt_version_label = QLabel(qt_version)
        tech_layout.addWidget(self.qt_version_label, 2, 1)

        layout.addWidget(tech_group)
        layout.addStretch()

        self.setLayout(layout)
        self.update_language()
        self.update_theme()

    def update_theme(self):
        theme = theme_manager.get_current_theme()

        # 更新页面标题样式
        self.page_title.setStyleSheet(f"""
            QLabel {{
                color: {theme['text_color']};
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 10px;
            }}
        """)

        # 更新应用信息卡片样式
        info_card = self.findChild(QFrame)
        if info_card:
            info_card.setStyleSheet(theme_manager.get_style_sheet("card"))

        # 更新应用名称样式
        self.app_name.setStyleSheet(f"""
            QLabel {{
                color: {theme['text_color']};
                font-size: 20px;
                font-weight: bold;
                background: transparent;
            }}
        """)

        # 更新其他标签样式
        for label in [self.app_version, self.app_author, self.app_description]:
            label.setStyleSheet(f"""
                QLabel {{
                    color: {theme['text_secondary']};
                    font-size: 12px;
                    background: transparent;
                }}
            """)

        # 更新技术信息组样式
        tech_group = self.findChild(QGroupBox)
        if tech_group:
            tech_group.setStyleSheet(f"""
                QGroupBox {{
                    background-color: {theme['card_color']};
                    border: 1px solid {theme['border_color']};
                    border-radius: 8px;
                    margin-top: 10px;
                    padding-top: 10px;
                    font-weight: bold;
                    color: {theme['text_color']};
                }}
                QGroupBox::title {{
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                }}
            """)

        # 更新技术信息标签样式
        for label in [self.framework_label, self.python_version_label, self.qt_version_label]:
            label.setStyleSheet(f"""
                QLabel {{
                    color: {theme['text_color']};
                    font-size: 12px;
                    background: transparent;
                }}
            """)

    def update_language(self):
        self.page_title.setText(i18n_manager.get_text("about_title"))
        self.app_name.setText(i18n_manager.get_text("app_name"))
        self.app_version.setText(i18n_manager.get_text("app_version"))
        self.app_author.setText(i18n_manager.get_text("app_author"))
        self.app_description.setText(i18n_manager.get_text("app_description_long"))

        # 更新技术信息组标题
        tech_group = self.findChild(QGroupBox)
        if tech_group:
            tech_group.setTitle(i18n_manager.get_text("tech_info"))

class EnhancedNavigationPanel(FluentNavigationPanel):
    def __init__(self):
        super().__init__()
        self.setup_enhanced_navigation()
        theme_manager.theme_changed.connect(self.update_theme)
        i18n_manager.language_changed.connect(self.update_language)

    def setup_enhanced_navigation(self):
        # 清除原有导航项
        for item in self.nav_items:
            item.setParent(None)
        self.nav_items.clear()

        # 重新创建导航项
        nav_data = [
            ("🏠", "nav_home"),
            ("🧩", "nav_components"),
            ("🎨", "nav_themes"),
            ("⚙️", "nav_settings"),
            ("ℹ️", "nav_about")
        ]

        layout = self.layout()

        for icon, text_key in nav_data:
            item = FluentNavigationItem("", icon)
            item.text_key = text_key  # 存储文本键
            self.nav_items.append(item)
            layout.insertWidget(layout.count() - 1, item)  # 在stretch之前插入
            item.clicked.connect(lambda checked=False, i=item: self.on_nav_clicked(i))

        # 默认选中第一项
        if self.nav_items:
            self.nav_items[0].setChecked(True)

        self.update_language()

    def update_theme(self):
        theme = theme_manager.get_current_theme()
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {theme['surface_color']};
                border: none;
                border-radius: 8px;
            }}
        """)

    def update_language(self):
        # 更新标题
        title_label = self.findChild(QLabel)
        if title_label:
            title_label.setText(i18n_manager.get_text("nav_title", "导航"))

        # 更新导航项文本
        for item in self.nav_items:
            if hasattr(item, 'text_key'):
                text = i18n_manager.get_text(item.text_key)
                # 更新导航项的文本标签
                text_label = item.findChild(QLabel)
                if text_label and text_label.text() != item.icon_text:  # 不是图标标签
                    text_label.setText(text)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 创建页面
        self.pages = {
            0: HomePage(),
            1: ComponentsPage(),
            2: ThemePage(),
            3: SettingsPage(),
            4: AboutPage()
        }

        self.current_page_index = 0

        self.center_window()
        self.setup_ui()

        # 连接主题和语言变化信号
        theme_manager.theme_changed.connect(self.update_theme)
        i18n_manager.language_changed.connect(self.update_language)

    def center_window(self):
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def setup_ui(self):
        # 主容器
        main_widget = QWidget()
        self.setCentralWidget(main_widget)

        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 5)
        main_widget.setGraphicsEffect(shadow)

        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 标题栏
        self.title_bar = TitleBar(self)
        main_layout.addWidget(self.title_bar)

        # 主内容区域
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # 导航面板
        self.nav_panel = EnhancedNavigationPanel()
        content_layout.addWidget(self.nav_panel)

        # 页面容器
        self.page_container = QWidget()
        self.page_layout = QVBoxLayout(self.page_container)
        self.page_layout.setContentsMargins(0, 0, 0, 0)

        # 添加当前页面
        self.show_page(0)

        content_layout.addWidget(self.page_container)

        content_widget = QWidget()
        content_widget.setLayout(content_layout)
        main_layout.addWidget(content_widget)

        # 连接导航事件
        for i, item in enumerate(self.nav_panel.nav_items):
            item.clicked.connect(lambda checked=False, idx=i: self.show_page(idx))

        self.update_theme()

    def show_page(self, page_index):
        # 移除当前页面
        if self.page_layout.count() > 0:
            current_page = self.page_layout.itemAt(0).widget()
            if current_page:
                current_page.setParent(None)

        # 添加新页面
        if page_index in self.pages:
            self.page_layout.addWidget(self.pages[page_index])
            self.current_page_index = page_index

    def update_theme(self):
        theme = theme_manager.get_current_theme()

        # 更新主窗口样式
        main_widget = self.centralWidget()
        if main_widget:
            main_widget.setStyleSheet(theme_manager.get_style_sheet("main_window"))

    def update_language(self):
        # 语言变化时，所有页面会自动更新
        pass

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # 设置应用程序信息
    app.setApplicationName("Enhanced Fluent Design Demo")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("Fluent UI")

    # 设置应用程序字体
    font = QFont("Segoe UI", 9)
    app.setFont(font)

    # 创建主窗口
    window = MainWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
