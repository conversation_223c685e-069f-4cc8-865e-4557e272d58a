#!/usr/bin/env python3
"""
简单的Fluent UI测试
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor

class SimpleFluentWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Fluent UI Test")
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.resize(400, 300)
        
        # 主窗口部件
        main_widget = QWidget()
        main_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(30, 30, 30, 200);
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 30);
            }
        """)
        
        self.setCentralWidget(main_widget)
        
        # 布局
        layout = QVBoxLayout(main_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("Fluent Design UI")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 20px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 描述
        desc = QLabel("这是一个使用PySide6创建的\n无边框Fluent风格界面")
        desc.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 180);
                font-size: 14px;
                background: transparent;
                line-height: 1.5;
            }
        """)
        desc.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc)
        
        layout.addStretch()
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.start_pos = event.globalPosition().toPoint()
    
    def mouseMoveEvent(self, event):
        if hasattr(self, 'start_pos') and event.buttons() == Qt.LeftButton:
            delta = event.globalPosition().toPoint() - self.start_pos
            self.move(self.pos() + delta)
            self.start_pos = event.globalPosition().toPoint()

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = SimpleFluentWindow()
    window.show()
    
    print("Fluent UI 测试窗口已启动")
    print("按 Ctrl+C 或关闭窗口退出")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
