# Fluent Design UI with PySide6

这是一个使用PySide6创建的现代化无边框Fluent Design风格UI界面演示应用。

## 特性

- ✨ **无边框设计** - 自定义标题栏，支持窗口拖拽、最小化、最大化、关闭
- 🎨 **Fluent Design风格** - 现代化的设计语言，毛玻璃效果
- 🎯 **丰富的控件** - 包含多种自定义Fluent风格控件
- 📱 **响应式布局** - 适配不同窗口大小
- 🌙 **深色主题** - 优雅的深色界面设计
- ⚡ **流畅动画** - 平滑的交互动画效果

## 包含的控件

### 基础控件
- `FluentButton` - Fluent风格按钮（主要/次要）
- `FluentLineEdit` - 现代化输入框
- `FluentSlider` - 自定义滑块控件
- `FluentToggleButton` - 切换按钮
- `FluentProgressBar` - 进度条

### 布局控件
- `FluentCard` - 卡片容器
- `FluentSeparator` - 分隔符
- `FluentInfoBar` - 信息提示栏
- `FluentNavigationPanel` - 导航面板

### 窗口控件
- `TitleBar` - 自定义标题栏
- 无边框窗口支持

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行应用

### 基础版本
```bash
python main.py
```

### 完整演示版本
```bash
python fluent_app.py
```

## 文件结构

```
├── main.py              # 基础无边框Fluent UI示例
├── fluent_app.py        # 完整功能演示应用
├── fluent_widgets.py    # 自定义Fluent风格控件库
├── requirements.txt     # 项目依赖
└── README.md           # 项目说明
```

## 主要功能

### 1. 无边框窗口
- 自定义标题栏
- 窗口拖拽功能
- 最小化/最大化/关闭按钮
- 窗口阴影效果

### 2. Fluent Design元素
- 毛玻璃背景效果
- 现代化配色方案
- 圆角边框设计
- 悬停动画效果

### 3. 自定义控件
- 统一的设计语言
- 一致的交互体验
- 可复用的组件

### 4. 导航系统
- 侧边导航面板
- 页面切换功能
- 选中状态指示

## 自定义和扩展

### 添加新的控件
在 `fluent_widgets.py` 中添加新的控件类：

```python
class YourFluentWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setStyleSheet("""
            /* Fluent Design样式 */
        """)
```

### 修改主题颜色
在控件的样式表中修改颜色值：

```python
# 主色调
primary_color = "#0078d4"

# 背景色
background_color = "rgba(20, 20, 20, 240)"

# 文字颜色
text_color = "white"
```

### 添加新页面
在 `ContentArea` 类中添加新的页面内容：

```python
def create_your_page_content(self, layout):
    # 添加页面内容
    pass
```

## 技术栈

- **PySide6** - Qt6的Python绑定
- **Qt Framework** - 跨平台GUI框架
- **Python 3.7+** - 编程语言

## 兼容性

- Windows 10/11
- macOS 10.14+
- Linux (Ubuntu 18.04+)

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 截图

运行应用后，你将看到：
- 现代化的无边框窗口
- Fluent Design风格的界面
- 丰富的交互控件
- 流畅的动画效果
