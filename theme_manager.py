"""
主题管理器 - 支持多种主题切换
"""

from PySide6.QtCore import QObject, Signal
from PySide6.QtGui import QColor
from enum import Enum

class ThemeType(Enum):
    DARK = "dark"
    LIGHT = "light"
    BLUE = "blue"
    GREEN = "green"
    PURPLE = "purple"
    ORANGE = "orange"

class ThemeManager(QObject):
    theme_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.current_theme = ThemeType.DARK
        self.themes = self._init_themes()
    
    def _init_themes(self):
        return {
            ThemeType.DARK: {
                "name": "深色主题",
                "primary_color": "#0078d4",
                "secondary_color": "#106ebe",
                "accent_color": "#005a9e",
                "background_color": "rgba(20, 20, 20, 240)",
                "surface_color": "rgba(30, 30, 30, 200)",
                "card_color": "rgba(255, 255, 255, 8)",
                "border_color": "rgba(255, 255, 255, 15)",
                "text_color": "white",
                "text_secondary": "rgba(255, 255, 255, 180)",
                "text_disabled": "rgba(255, 255, 255, 120)",
                "hover_color": "rgba(255, 255, 255, 10)",
                "pressed_color": "rgba(255, 255, 255, 20)",
                "shadow_color": "rgba(0, 0, 0, 100)"
            },
            ThemeType.LIGHT: {
                "name": "浅色主题",
                "primary_color": "#0078d4",
                "secondary_color": "#106ebe",
                "accent_color": "#005a9e",
                "background_color": "rgba(248, 249, 250, 240)",
                "surface_color": "rgba(255, 255, 255, 200)",
                "card_color": "rgba(255, 255, 255, 80)",
                "border_color": "rgba(0, 0, 0, 15)",
                "text_color": "#323130",
                "text_secondary": "rgba(50, 49, 48, 180)",
                "text_disabled": "rgba(50, 49, 48, 120)",
                "hover_color": "rgba(0, 0, 0, 5)",
                "pressed_color": "rgba(0, 0, 0, 10)",
                "shadow_color": "rgba(0, 0, 0, 50)"
            },
            ThemeType.BLUE: {
                "name": "蓝色主题",
                "primary_color": "#0078d4",
                "secondary_color": "#106ebe",
                "accent_color": "#005a9e",
                "background_color": "rgba(15, 25, 45, 240)",
                "surface_color": "rgba(25, 35, 55, 200)",
                "card_color": "rgba(0, 120, 212, 8)",
                "border_color": "rgba(0, 120, 212, 25)",
                "text_color": "#e1f5fe",
                "text_secondary": "rgba(225, 245, 254, 180)",
                "text_disabled": "rgba(225, 245, 254, 120)",
                "hover_color": "rgba(0, 120, 212, 10)",
                "pressed_color": "rgba(0, 120, 212, 20)",
                "shadow_color": "rgba(0, 50, 100, 100)"
            },
            ThemeType.GREEN: {
                "name": "绿色主题",
                "primary_color": "#107c10",
                "secondary_color": "#0e6e0e",
                "accent_color": "#0c5f0c",
                "background_color": "rgba(15, 30, 15, 240)",
                "surface_color": "rgba(25, 40, 25, 200)",
                "card_color": "rgba(16, 124, 16, 8)",
                "border_color": "rgba(16, 124, 16, 25)",
                "text_color": "#e8f5e8",
                "text_secondary": "rgba(232, 245, 232, 180)",
                "text_disabled": "rgba(232, 245, 232, 120)",
                "hover_color": "rgba(16, 124, 16, 10)",
                "pressed_color": "rgba(16, 124, 16, 20)",
                "shadow_color": "rgba(0, 50, 0, 100)"
            },
            ThemeType.PURPLE: {
                "name": "紫色主题",
                "primary_color": "#8764b8",
                "secondary_color": "#7659a6",
                "accent_color": "#654e94",
                "background_color": "rgba(25, 15, 35, 240)",
                "surface_color": "rgba(35, 25, 45, 200)",
                "card_color": "rgba(135, 100, 184, 8)",
                "border_color": "rgba(135, 100, 184, 25)",
                "text_color": "#f3e8ff",
                "text_secondary": "rgba(243, 232, 255, 180)",
                "text_disabled": "rgba(243, 232, 255, 120)",
                "hover_color": "rgba(135, 100, 184, 10)",
                "pressed_color": "rgba(135, 100, 184, 20)",
                "shadow_color": "rgba(50, 0, 100, 100)"
            },
            ThemeType.ORANGE: {
                "name": "橙色主题",
                "primary_color": "#ff8c00",
                "secondary_color": "#e67e00",
                "accent_color": "#cc7000",
                "background_color": "rgba(35, 25, 15, 240)",
                "surface_color": "rgba(45, 35, 25, 200)",
                "card_color": "rgba(255, 140, 0, 8)",
                "border_color": "rgba(255, 140, 0, 25)",
                "text_color": "#fff4e6",
                "text_secondary": "rgba(255, 244, 230, 180)",
                "text_disabled": "rgba(255, 244, 230, 120)",
                "hover_color": "rgba(255, 140, 0, 10)",
                "pressed_color": "rgba(255, 140, 0, 20)",
                "shadow_color": "rgba(100, 50, 0, 100)"
            }
        }
    
    def get_current_theme(self):
        return self.themes[self.current_theme]
    
    def get_theme_names(self):
        return {theme_type: theme_data["name"] for theme_type, theme_data in self.themes.items()}
    
    def set_theme(self, theme_type):
        if theme_type in self.themes:
            self.current_theme = theme_type
            self.theme_changed.emit(theme_type.value)
    
    def get_theme_by_name(self, name):
        for theme_type, theme_data in self.themes.items():
            if theme_data["name"] == name:
                return theme_type
        return ThemeType.DARK
    
    def get_style_sheet(self, widget_type="default"):
        """根据当前主题生成样式表"""
        theme = self.get_current_theme()
        
        if widget_type == "main_window":
            return f"""
                QWidget {{
                    background-color: {theme['background_color']};
                    border-radius: 10px;
                }}
            """
        elif widget_type == "title_bar":
            return f"""
                QWidget {{
                    background-color: {theme['surface_color']};
                    border: none;
                }}
            """
        elif widget_type == "button_primary":
            return f"""
                QPushButton {{
                    background-color: {theme['primary_color']};
                    color: white;
                    border: 1px solid {theme['primary_color']};
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: 600;
                }}
                QPushButton:hover {{
                    background-color: {theme['secondary_color']};
                    border-color: {theme['secondary_color']};
                }}
                QPushButton:pressed {{
                    background-color: {theme['accent_color']};
                    border-color: {theme['accent_color']};
                }}
            """
        elif widget_type == "button_secondary":
            return f"""
                QPushButton {{
                    background-color: {theme['hover_color']};
                    color: {theme['text_color']};
                    border: 1px solid {theme['border_color']};
                    border-radius: 4px;
                    padding: 8px 16px;
                }}
                QPushButton:hover {{
                    background-color: {theme['pressed_color']};
                    border-color: {theme['border_color']};
                }}
                QPushButton:pressed {{
                    background-color: {theme['pressed_color']};
                }}
            """
        elif widget_type == "line_edit":
            return f"""
                QLineEdit {{
                    background-color: {theme['hover_color']};
                    border: 1px solid {theme['border_color']};
                    border-radius: 4px;
                    padding: 8px 12px;
                    color: {theme['text_color']};
                    font-size: 12px;
                }}
                QLineEdit:focus {{
                    border: 2px solid {theme['primary_color']};
                    background-color: {theme['card_color']};
                }}
                QLineEdit::placeholder {{
                    color: {theme['text_disabled']};
                }}
            """
        elif widget_type == "card":
            return f"""
                QFrame {{
                    background-color: {theme['card_color']};
                    border: 1px solid {theme['border_color']};
                    border-radius: 8px;
                    padding: 15px;
                }}
            """
        elif widget_type == "label_primary":
            return f"""
                QLabel {{
                    color: {theme['text_color']};
                    background: transparent;
                }}
            """
        elif widget_type == "label_secondary":
            return f"""
                QLabel {{
                    color: {theme['text_secondary']};
                    background: transparent;
                }}
            """
        
        return ""

# 全局主题管理器实例
theme_manager = ThemeManager()
