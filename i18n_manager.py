"""
国际化管理器 - 支持中英文切换
"""

from PySide6.QtCore import QObject, Signal
from enum import Enum

class Language(Enum):
    CHINESE = "zh_CN"
    ENGLISH = "en_US"

class I18nManager(QObject):
    language_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.current_language = Language.CHINESE
        self.translations = self._init_translations()
    
    def _init_translations(self):
        return {
            Language.CHINESE: {
                # 窗口标题
                "app_title": "Fluent Design 演示",
                "window_title": "Fluent Design Demo",
                
                # 导航
                "nav_home": "首页",
                "nav_components": "组件",
                "nav_themes": "主题",
                "nav_settings": "设置",
                "nav_about": "关于",
                
                # 首页
                "home_title": "首页",
                "home_welcome": "欢迎使用 Fluent Design 演示应用！",
                "home_description": "这是一个使用PySide6创建的现代化无边框界面",
                "home_features": "功能特性",
                "home_tech_stack": "技术栈",
                "home_highlights": "特色",
                
                # 组件页面
                "components_title": "组件展示",
                "components_description": "各种 Fluent Design 风格的控件演示",
                "input_controls": "输入控件",
                "display_controls": "显示控件",
                "layout_controls": "布局控件",
                "feedback_controls": "反馈控件",
                
                # 控件标签
                "text_input": "文本输入",
                "password_input": "密码输入",
                "number_input": "数字输入",
                "multiline_input": "多行输入",
                "slider_control": "滑块控件",
                "toggle_button": "切换按钮",
                "checkbox": "复选框",
                "radio_button": "单选按钮",
                "combo_box": "下拉框",
                "progress_bar": "进度条",
                "rating_control": "评分控件",
                "date_picker": "日期选择",
                "time_picker": "时间选择",
                "color_picker": "颜色选择",
                
                # 主题页面
                "themes_title": "主题设置",
                "themes_description": "选择您喜欢的主题风格",
                "theme_preview": "主题预览",
                "apply_theme": "应用主题",
                "theme_dark": "深色主题",
                "theme_light": "浅色主题",
                "theme_blue": "蓝色主题",
                "theme_green": "绿色主题",
                "theme_purple": "紫色主题",
                "theme_orange": "橙色主题",
                
                # 设置页面
                "settings_title": "设置",
                "settings_description": "应用程序设置和偏好",
                "language_settings": "语言设置",
                "appearance_settings": "外观设置",
                "behavior_settings": "行为设置",
                "language": "语言",
                "theme": "主题",
                "auto_start": "开机自启",
                "minimize_to_tray": "最小化到托盘",
                "enable_animations": "启用动画",
                "enable_sounds": "启用声音",
                
                # 关于页面
                "about_title": "关于",
                "about_description": "关于此应用程序",
                "app_name": "Fluent Design Demo",
                "app_version": "版本 1.0.0",
                "app_author": "作者：开发者",
                "app_description_long": "这是一个使用 PySide6 和 Qt 框架创建的现代化桌面应用程序，展示了 Fluent Design 设计语言的各种元素和控件。",
                "tech_info": "技术信息",
                "framework": "框架：PySide6",
                "python_version": "Python 版本",
                "qt_version": "Qt 版本",
                
                # 按钮
                "primary_action": "主要操作",
                "secondary_action": "次要操作",
                "cancel": "取消",
                "confirm": "确认",
                "apply": "应用",
                "reset": "重置",
                "save": "保存",
                "close": "关闭",
                "ok": "确定",
                "yes": "是",
                "no": "否",
                
                # 占位符
                "placeholder_text": "请输入文本...",
                "placeholder_password": "请输入密码...",
                "placeholder_number": "请输入数字...",
                "placeholder_search": "搜索...",
                
                # 消息
                "info_message": "这是一条信息消息",
                "success_message": "操作成功完成！",
                "warning_message": "请注意这个警告",
                "error_message": "发生了一个错误",
                
                # 功能描述
                "feature_1": "无边框设计",
                "feature_1_desc": "自定义标题栏和窗口控制",
                "feature_2": "现代化UI",
                "feature_2_desc": "Fluent Design 设计语言",
                "feature_3": "主题切换",
                "feature_3_desc": "多种主题风格可选",
                "feature_4": "国际化",
                "feature_4_desc": "支持多语言切换",
                "feature_5": "丰富组件",
                "feature_5_desc": "各种现代化控件",
                "feature_6": "响应式",
                "feature_6_desc": "适配不同屏幕尺寸"
            },
            Language.ENGLISH: {
                # Window titles
                "app_title": "Fluent Design Demo",
                "window_title": "Fluent Design Demo",
                
                # Navigation
                "nav_home": "Home",
                "nav_components": "Components",
                "nav_themes": "Themes",
                "nav_settings": "Settings",
                "nav_about": "About",
                
                # Home page
                "home_title": "Home",
                "home_welcome": "Welcome to Fluent Design Demo Application!",
                "home_description": "A modern frameless interface created with PySide6",
                "home_features": "Features",
                "home_tech_stack": "Tech Stack",
                "home_highlights": "Highlights",
                
                # Components page
                "components_title": "Components",
                "components_description": "Demonstration of various Fluent Design style controls",
                "input_controls": "Input Controls",
                "display_controls": "Display Controls",
                "layout_controls": "Layout Controls",
                "feedback_controls": "Feedback Controls",
                
                # Control labels
                "text_input": "Text Input",
                "password_input": "Password Input",
                "number_input": "Number Input",
                "multiline_input": "Multiline Input",
                "slider_control": "Slider Control",
                "toggle_button": "Toggle Button",
                "checkbox": "Checkbox",
                "radio_button": "Radio Button",
                "combo_box": "Combo Box",
                "progress_bar": "Progress Bar",
                "rating_control": "Rating Control",
                "date_picker": "Date Picker",
                "time_picker": "Time Picker",
                "color_picker": "Color Picker",
                
                # Themes page
                "themes_title": "Theme Settings",
                "themes_description": "Choose your preferred theme style",
                "theme_preview": "Theme Preview",
                "apply_theme": "Apply Theme",
                "theme_dark": "Dark Theme",
                "theme_light": "Light Theme",
                "theme_blue": "Blue Theme",
                "theme_green": "Green Theme",
                "theme_purple": "Purple Theme",
                "theme_orange": "Orange Theme",
                
                # Settings page
                "settings_title": "Settings",
                "settings_description": "Application settings and preferences",
                "language_settings": "Language Settings",
                "appearance_settings": "Appearance Settings",
                "behavior_settings": "Behavior Settings",
                "language": "Language",
                "theme": "Theme",
                "auto_start": "Auto Start",
                "minimize_to_tray": "Minimize to Tray",
                "enable_animations": "Enable Animations",
                "enable_sounds": "Enable Sounds",
                
                # About page
                "about_title": "About",
                "about_description": "About this application",
                "app_name": "Fluent Design Demo",
                "app_version": "Version 1.0.0",
                "app_author": "Author: Developer",
                "app_description_long": "This is a modern desktop application created with PySide6 and Qt framework, showcasing various elements and controls of the Fluent Design language.",
                "tech_info": "Technical Information",
                "framework": "Framework: PySide6",
                "python_version": "Python Version",
                "qt_version": "Qt Version",
                
                # Buttons
                "primary_action": "Primary Action",
                "secondary_action": "Secondary Action",
                "cancel": "Cancel",
                "confirm": "Confirm",
                "apply": "Apply",
                "reset": "Reset",
                "save": "Save",
                "close": "Close",
                "ok": "OK",
                "yes": "Yes",
                "no": "No",
                
                # Placeholders
                "placeholder_text": "Enter text...",
                "placeholder_password": "Enter password...",
                "placeholder_number": "Enter number...",
                "placeholder_search": "Search...",
                
                # Messages
                "info_message": "This is an information message",
                "success_message": "Operation completed successfully!",
                "warning_message": "Please note this warning",
                "error_message": "An error occurred",
                
                # Feature descriptions
                "feature_1": "Frameless Design",
                "feature_1_desc": "Custom title bar and window controls",
                "feature_2": "Modern UI",
                "feature_2_desc": "Fluent Design language",
                "feature_3": "Theme Switching",
                "feature_3_desc": "Multiple theme styles available",
                "feature_4": "Internationalization",
                "feature_4_desc": "Multi-language support",
                "feature_5": "Rich Components",
                "feature_5_desc": "Various modern controls",
                "feature_6": "Responsive",
                "feature_6_desc": "Adapts to different screen sizes"
            }
        }
    
    def get_text(self, key, default=""):
        """获取当前语言的文本"""
        return self.translations[self.current_language].get(key, default or key)
    
    def set_language(self, language):
        """设置语言"""
        if language in self.translations:
            self.current_language = language
            self.language_changed.emit(language.value)
    
    def get_current_language(self):
        """获取当前语言"""
        return self.current_language
    
    def get_available_languages(self):
        """获取可用语言列表"""
        return {
            Language.CHINESE: "中文",
            Language.ENGLISH: "English"
        }

# 全局国际化管理器实例
i18n_manager = I18nManager()
